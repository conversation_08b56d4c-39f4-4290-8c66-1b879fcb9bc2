#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=USART1_RX
Dma.Request1=USART1_TX
Dma.Request2=USART3_RX
Dma.Request3=USART3_TX
Dma.RequestsNb=4
Dma.USART1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.0.Instance=DMA2_Stream2
Dma.USART1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.0.Mode=DMA_NORMAL
Dma.USART1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART1_TX.1.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART1_TX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_TX.1.Instance=DMA2_Stream7
Dma.USART1_TX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_TX.1.MemInc=DMA_MINC_ENABLE
Dma.USART1_TX.1.Mode=DMA_NORMAL
Dma.USART1_TX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_TX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_TX.1.Priority=DMA_PRIORITY_LOW
Dma.USART1_TX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.2.Instance=DMA1_Stream1
Dma.USART3_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.2.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.2.Mode=DMA_NORMAL
Dma.USART3_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.2.Priority=DMA_PRIORITY_LOW
Dma.USART3_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_TX.3.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART3_TX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_TX.3.Instance=DMA1_Stream3
Dma.USART3_TX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_TX.3.MemInc=DMA_MINC_ENABLE
Dma.USART3_TX.3.Mode=DMA_NORMAL
Dma.USART3_TX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_TX.3.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_TX.3.Priority=DMA_PRIORITY_LOW
Dma.USART3_TX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
FREERTOS.BinarySemaphores01=I2CMutex,Dynamic,NULL,Available;UARTMutex,Dynamic,NULL,Available;DisplayMutex,Dynamic,NULL,Available;Esp01sMutex,Dynamic,NULL,Available
FREERTOS.FootprintOK=true
FREERTOS.IPParameters=Tasks01,FootprintOK,Queues01,BinarySemaphores01,configUSE_NEWLIB_REENTRANT,Timers01,configENABLE_FPU,configTOTAL_HEAP_SIZE
FREERTOS.Queues01=KeyEventQueue,10,uint32_t,0,Dynamic,NULL,NULL;WiFiCmdQueue,5,32,1,Dynamic,NULL,NULL;SensorDataQueue,3,24,1,Dynamic,NULL,NULL
FREERTOS.Tasks01=defaultTask,24,128,StartDefaultTask,Default,NULL,Dynamic,NULL,NULL;MotorTask,44,512,StartMotorTask,As weak,NULL,Dynamic,NULL,NULL;SensorTask,44,256,StartSensorTask,As weak,NULL,Dynamic,NULL,NULL;OledTask,26,512,StartOledTask,As weak,NULL,Dynamic,NULL,NULL;KeyTask,26,256,StartKeyTask,As weak,NULL,Dynamic,NULL,NULL;SerialTask,26,512,StartSerialTask,As weak,NULL,Dynamic,NULL,NULL;UltrasonicTask,9,256,StartUltrasonicTask,As weak,NULL,Dynamic,NULL,NULL
FREERTOS.Timers01=KeyScanTimer,KeyScanTimerCallback,osTimerPeriodic,As weak,NULL,Dynamic,NULL;HeartbeatTimer,HeartbeatTimerCallback,osTimerPeriodic,As weak,NULL,Dynamic,NULL
FREERTOS.configENABLE_FPU=1
FREERTOS.configTOTAL_HEAP_SIZE=32768
FREERTOS.configUSE_NEWLIB_REENTRANT=1
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C2.I2C_Mode=I2C_Fast
I2C2.IPParameters=I2C_Mode
KeepUserPlacement=false
Mcu.CPN=STM32F407VET6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=FREERTOS
Mcu.IP10=USART1
Mcu.IP11=USART3
Mcu.IP2=I2C1
Mcu.IP3=I2C2
Mcu.IP4=NVIC
Mcu.IP5=RCC
Mcu.IP6=SYS
Mcu.IP7=TIM3
Mcu.IP8=TIM4
Mcu.IP9=TIM5
Mcu.IPNb=12
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PB1
Mcu.Pin11=PB10
Mcu.Pin12=PB11
Mcu.Pin13=PD8
Mcu.Pin14=PD9
Mcu.Pin15=PC6
Mcu.Pin16=PC7
Mcu.Pin17=PA9
Mcu.Pin18=PA10
Mcu.Pin19=PA13
Mcu.Pin2=PE4
Mcu.Pin20=PA14
Mcu.Pin21=PD0
Mcu.Pin22=PD1
Mcu.Pin23=PD2
Mcu.Pin24=PD3
Mcu.Pin25=PD4
Mcu.Pin26=PD5
Mcu.Pin27=PD6
Mcu.Pin28=PB6
Mcu.Pin29=PB7
Mcu.Pin3=PC14-OSC32_IN
Mcu.Pin30=PB8
Mcu.Pin31=PB9
Mcu.Pin32=VP_FREERTOS_VS_CMSIS_V2
Mcu.Pin33=VP_SYS_VS_tim6
Mcu.Pin34=VP_TIM3_VS_ClockSourceINT
Mcu.Pin35=VP_TIM4_VS_ClockSourceINT
Mcu.Pin36=VP_TIM5_VS_ClockSourceINT
Mcu.Pin4=PC15-OSC32_OUT
Mcu.Pin5=PH0-OSC_IN
Mcu.Pin6=PH1-OSC_OUT
Mcu.Pin7=PA2
Mcu.Pin8=PA3
Mcu.Pin9=PB0
Mcu.PinsNb=37
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VETx
MxCube.Version=6.15.0
MxDb.Version=DB.6.0.150
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.DMA1_Stream1_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream3_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:9\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream7_IRQn=true\:9\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.I2C1_EV_IRQn=true\:8\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.I2C2_EV_IRQn=true\:8\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true\:false
NVIC.TIM3_IRQn=true\:2\:0\:true\:false\:true\:false\:true\:true\:true
NVIC.TIM4_IRQn=true\:3\:0\:true\:false\:true\:false\:true\:true\:true
NVIC.TIM5_IRQn=true\:2\:0\:true\:false\:true\:false\:true\:true\:true
NVIC.TIM6_DAC_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TimeBase=TIM6_DAC_IRQn
NVIC.TimeBaseIP=TIM6
NVIC.USART1_IRQn=true\:6\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:7\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Locked=true
PA2.Signal=S_TIM5_CH3
PA3.Locked=true
PA3.Signal=S_TIM5_CH4
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.Signal=S_TIM3_CH3
PB1.Signal=S_TIM3_CH4
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PB8.GPIOParameters=GPIO_Label
PB8.GPIO_Label=TRIG
PB8.Locked=true
PB8.Signal=GPIO_Output
PB9.GPIOParameters=GPIO_Label
PB9.GPIO_Label=ECHO
PB9.Locked=true
PB9.Signal=S_TIM4_CH4
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC6.Locked=true
PC6.Signal=S_TIM3_CH1
PC7.Locked=true
PC7.Signal=S_TIM3_CH2
PD0.GPIOParameters=GPIO_Label
PD0.GPIO_Label=AIN1
PD0.Locked=true
PD0.Signal=GPIO_Output
PD1.GPIOParameters=GPIO_Label
PD1.GPIO_Label=AIN2
PD1.Locked=true
PD1.Signal=GPIO_Output
PD2.GPIOParameters=GPIO_Label
PD2.GPIO_Label=BIN1
PD2.Locked=true
PD2.Signal=GPIO_Output
PD3.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PD3.GPIO_Label=LED1
PD3.GPIO_PuPd=GPIO_PULLUP
PD3.Locked=true
PD3.PinState=GPIO_PIN_SET
PD3.Signal=GPIO_Output
PD4.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PD4.GPIO_Label=LED2
PD4.GPIO_PuPd=GPIO_PULLUP
PD4.Locked=true
PD4.PinState=GPIO_PIN_SET
PD4.Signal=GPIO_Output
PD5.GPIOParameters=GPIO_Label
PD5.GPIO_Label=BIN2
PD5.Locked=true
PD5.Signal=GPIO_Output
PD6.GPIOParameters=GPIO_Label
PD6.GPIO_Label=STBY
PD6.Locked=true
PD6.Signal=GPIO_Output
PD8.Mode=Asynchronous
PD8.Signal=USART3_TX
PD9.Mode=Asynchronous
PD9.Signal=USART3_RX
PE2.GPIOParameters=GPIO_PuPd,GPIO_Label
PE2.GPIO_Label=KEY1
PE2.GPIO_PuPd=GPIO_PULLUP
PE2.Locked=true
PE2.Signal=GPIO_Input
PE3.GPIOParameters=GPIO_PuPd,GPIO_Label
PE3.GPIO_Label=KEY2
PE3.GPIO_PuPd=GPIO_PULLUP
PE3.Locked=true
PE3.Signal=GPIO_Input
PE4.GPIOParameters=GPIO_PuPd,GPIO_Label
PE4.GPIO_Label=KEY3
PE4.GPIO_PuPd=GPIO_PULLUP
PE4.Locked=true
PE4.Signal=GPIO_Input
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=Car_pid.ioc
ProjectManager.ProjectName=Car_pid
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x800
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_I2C2_Init-I2C2-false-HAL-true,5-MX_I2C1_Init-I2C1-false-HAL-true,6-MX_USART1_UART_Init-USART1-false-HAL-true,7-MX_USART3_UART_Init-USART3-false-HAL-true,8-MX_TIM3_Init-TIM3-false-HAL-true,9-MX_TIM4_Init-TIM4-false-HAL-true,10-MX_TIM5_Init-TIM5-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=96000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=25
RCC.PLLN=336
RCC.PLLQCLKFreq_Value=84000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=12500000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=192000000
RCC.VCOInputFreq_Value=1000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=96000000
SH.S_TIM3_CH1.0=TIM3_CH1,PWM Generation1 CH1
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,PWM Generation2 CH2
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM3_CH3.0=TIM3_CH3,Input_Capture3_from_TI3
SH.S_TIM3_CH3.ConfNb=1
SH.S_TIM3_CH4.0=TIM3_CH4,Input_Capture4_from_TI4
SH.S_TIM3_CH4.ConfNb=1
SH.S_TIM4_CH4.0=TIM4_CH4,Input_Capture4_from_TI4
SH.S_TIM4_CH4.ConfNb=1
SH.S_TIM5_CH3.0=TIM5_CH3,Input_Capture3_from_TI3
SH.S_TIM5_CH3.ConfNb=1
SH.S_TIM5_CH4.0=TIM5_CH4,Input_Capture4_from_TI4
SH.S_TIM5_CH4.ConfNb=1
TIM3.Channel-Input_Capture3_from_TI3=TIM_CHANNEL_3
TIM3.Channel-Input_Capture4_from_TI4=TIM_CHANNEL_4
TIM3.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM3.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM3.IPParameters=Channel-PWM Generation1 CH1,Channel-PWM Generation2 CH2,Channel-Input_Capture3_from_TI3,Channel-Input_Capture4_from_TI4,Prescaler,Period
TIM3.Period=1000-1
TIM3.Prescaler=84-1
TIM4.Channel-Input_Capture4_from_TI4=TIM_CHANNEL_4
TIM4.IPParameters=Prescaler,Channel-Input_Capture4_from_TI4
TIM4.Prescaler=83
TIM5.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_DISABLE
TIM5.Channel-Input_Capture3_from_TI3=TIM_CHANNEL_3
TIM5.Channel-Input_Capture4_from_TI4=TIM_CHANNEL_4
TIM5.IPParameters=Channel-Input_Capture3_from_TI3,Channel-Input_Capture4_from_TI4,AutoReloadPreload
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
VP_FREERTOS_VS_CMSIS_V2.Mode=CMSIS_V2
VP_FREERTOS_VS_CMSIS_V2.Signal=FREERTOS_VS_CMSIS_V2
VP_SYS_VS_tim6.Mode=TIM6
VP_SYS_VS_tim6.Signal=SYS_VS_tim6
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
VP_TIM4_VS_ClockSourceINT.Mode=Internal
VP_TIM4_VS_ClockSourceINT.Signal=TIM4_VS_ClockSourceINT
VP_TIM5_VS_ClockSourceINT.Mode=Internal
VP_TIM5_VS_ClockSourceINT.Signal=TIM5_VS_ClockSourceINT
board=custom
rtos.0.ip=FREERTOS
isbadioc=false
