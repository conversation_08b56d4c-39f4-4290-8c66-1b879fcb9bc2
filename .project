<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>Car_pid</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.st.stm32cube.ide.mcu.MCUProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUCubeProjectNature</nature>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUCubeIdeServicesRevAev2ProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUAdvancedStructureProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUSingleCpuProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCURootProjectNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUNonUnderRootProjectNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>Drivers/CMSIS/system_stm32f4xx.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Core/Src/system_stm32f4xx.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_cortex.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_dma.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_dma_ex.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_exti.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_flash.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_flash_ex.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_flash_ramfunc.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_gpio.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_i2c.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_i2c_ex.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_pwr.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_pwr_ex.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_rcc.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_rcc_ex.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_tim.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_tim_ex.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_uart.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/dma.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Core/Src/dma.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/freertos.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Core/Src/freertos.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/gpio.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Core/Src/gpio.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/i2c.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Core/Src/i2c.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/main.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Core/Src/main.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/stm32f4xx_hal_msp.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Core/Src/stm32f4xx_hal_msp.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/stm32f4xx_hal_timebase_tim.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Core/Src/stm32f4xx_hal_timebase_tim.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/stm32f4xx_it.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Core/Src/stm32f4xx_it.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/tim.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Core/Src/tim.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/usart.c</name>
			<type>1</type>
			<locationURI>PROJECT_LOC/Core/Src/usart.c</locationURI>
		</link>
	</linkedResources>
</projectDescription>
