#ifndef MOTOR_DRIVER_H
#define MOTOR_DRIVER_H

#include "sys.h"
#include "pid.h"
#include "encoder.h"
#include "mpu_data.h"


// 电机控制参数定义
#define MOTOR_PWM_MAX           1000    // PWM最大值(对应TIM3 ARR)
#define MOTOR_SPEED_MAX         1000    // 电机最大速度值
#define MOTOR_DEADZONE          50      // 电机死区，避免低速抖动

// WiFi遥控指令定义
#define WIFI_CMD_FORWARD        '1'     // 前进
#define WIFI_CMD_BACKWARD       '2'     // 后退
#define WIFI_CMD_LEFT           '3'     // 左转
#define WIFI_CMD_RIGHT          '4'     // 右转
#define WIFI_CMD_STOP           '0'     // 停止

// 电机控制数据结构
typedef struct {
    int16_t left_speed;     // 左电机速度 (-1000~1000)
    int16_t right_speed;    // 右电机速度 (-1000~1000)
    uint8_t enable;         // 电机使能标志
    uint8_t wifi_cmd;       // 当前WiFi指令
} Motor_Control_t;

// PID控制器结构
typedef struct {
    PID_t balance_pid;      // 平衡PID控制器
    PID_t velocity_pid;     // 速度PID控制器
    PID_t turn_pid;         // 转向PID控制器
} Motor_PID_t;

// 函数声明
void motor_init(void);                                    // 电机初始化
void motor_control(int16_t left_speed, int16_t right_speed); // 电机控制
void motor_set_speed(uint8_t motor_id, int16_t speed);    // 设置单个电机速度
void motor_stop(void);                                    // 电机停止
void motor_enable(uint8_t enable);                        // 电机使能控制
uint8_t motor_get_wifi_command(void);                     // 获取WiFi指令
void motor_pid_init(void);                               // PID控制器初始化
void motor_pid_calculate(float pitch, float target_velocity, float turn_rate); // PID计算
void process_global_wifi_command(void);                   // 统一WiFi指令处理器
void motor_process_wifi_pid_tuning(const char* json_data); // WiFi调参处理
void motor_calibrate_mechanical_zero(void);               // 机械中值校准
float motor_get_mechanical_zero(void);                    // 获取机械中值
void motor_control_smooth(int16_t left_target, int16_t right_target); // 平滑电机控制

// 全局变量声明
extern Motor_Control_t motor_ctrl;
extern Motor_PID_t motor_pid;

#endif //MOTOR_DRIVER_H
