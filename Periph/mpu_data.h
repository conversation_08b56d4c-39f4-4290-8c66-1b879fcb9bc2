//
// Created by 点点苍穹 on 25-7-23.
//

#ifndef MPU_DATA_H
#define MPU_DATA_H

#include "sys.h"

typedef struct {
    float pitch;    // 俯仰角
    float roll;     // 横滚角
    float yaw;      // 偏航角
} MPU6050_Data_t;

// 卡尔曼滤波器状态结构体
typedef struct {
    float Q_angle;   // 过程噪声协方差
    float Q_bias;    // 过程噪声协方差
    float R_measure; // 测量噪声协方差
    float angle;     // 角度
    float bias;      // 偏差
    float rate;      // 角速度
    float P[2][2];   // 误差协方差矩阵
} Kalman_t;

// 自适应滤波器结构体
typedef struct {
    float alpha;        // 滤波系数
    float prev_value;   // 上一次值
    float variance;     // 方差
    uint8_t init;       // 初始化标志
} Adaptive_Filter_t;

// 高级滤波器组合结构体
typedef struct {
    Kalman_t pitch_kalman;      // pitch卡尔曼滤波器
    Kalman_t roll_kalman;       // roll卡尔曼滤波器
    Adaptive_Filter_t pitch_adaptive;  // pitch自适应滤波器
    Adaptive_Filter_t roll_adaptive;   // roll自适应滤波器
    float complementary_alpha;  // 互补滤波系数
} Advanced_Filter_t;

typedef struct {
    int g_mpu_state;    // 0:成功  <0:失败
    int count;          // 重试计数器
} MPU6050_State_t;

extern osMutexId_t I2CMutexHandle;
extern osMessageQueueId_t SensorDataQueueHandle;
extern MPU6050_State_t g_mpu_state;

// 函数声明
MPU6050_Data_t get_mpu_data(void);
void advanced_filter_init(void);                    // 高级滤波器初始化
float kalman_filter(Kalman_t *kalman, float newAngle, float newRate, float dt); // 卡尔曼滤波
float adaptive_filter(Adaptive_Filter_t *filter, float input); // 自适应滤波
float complementary_filter(float accel_angle, float gyro_rate, float dt, float alpha); // 互补滤波

#endif //MPU_DATA_H
