#ifndef SYS_H
#define SYS_H

#include "dma.h"
#include "i2c.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"
#include "oled.h"
#include "mpu6050.h"

#include "FreeRTOS.h"
#include "task.h"
#include "cmsis_os.h"

#include "uart.h"
#include "mpu_data.h"
#include "ui.h"
#include "key.h"
#include "esp.h"
#include "pid.h"
#include "json.h"

//错误码
#define ESP_ERROR   -00010
#define MPU_ERROR   -00020
#define PID_ERROR   -00030
#define UI_ERROR    -00040
#define KEY_ERROR   -00050

#endif //SYS_H
