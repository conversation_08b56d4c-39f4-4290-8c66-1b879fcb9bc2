#include "pid.h"

void pid_update(PID_t *pid) {
    // 保存上一次误差
    pid->error1 = pid->error0;

    // 计算当前误差：目标值 - 实际值
    pid->error0 = pid->target - pid->actual;

    // 积分项计算（梯形积分）
    if (pid->Ki != 0) {
        pid->errorInt += pid->error0; // 简化为矩形积分，更稳定

        // 积分限幅，防止积分饱和
        float int_max = pid->maximum / pid->Ki;
        float int_min = pid->minimum / pid->Ki;
        if (pid->errorInt > int_max) pid->errorInt = int_max;
        if (pid->errorInt < int_min) pid->errorInt = int_min;
    } else {
        pid->errorInt = 0;
    }

    // PID输出计算：P + I + D
    pid->output = pid->Kp * pid->error0 +           // 比例项：当前误差
                  pid->Ki * pid->errorInt +         // 积分项：误差累积
                  pid->Kd * (pid->error0 - pid->error1); // 微分项：误差变化率

    // 输出死区处理，减少小幅震荡
    if (pid->output > -5.0f && pid->output < 5.0f) {
        pid->output = 0.0f; // 小于5的输出设为0
    }

    // 输出限幅
    if (pid->output > pid->maximum)
        pid->output = pid->maximum;
    else if (pid->output < pid->minimum)
        pid->output = pid->minimum;
}
