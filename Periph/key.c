#include "key.h"

// 全局变量定义
Key_State_t key_state = {0};

/**
 * @brief 按键初始化
 */
void key_init(void) {
    // GPIO已在CubeMX中配置，这里只需要初始化状态
    key_state.key1_pressed = 0;
    key_state.key2_pressed = 0;
    key_state.key3_pressed = 0;
    key_state.key1_press_time = 0;
    key_state.key2_press_time = 0;
    key_state.key3_press_time = 0;
    key_state.key1_long_handled = 0;
    key_state.key3_long_handled = 0;
}

/**
 * @brief 按键事件处理函数
 * @param event 按键事件
 */
void key_process_event(Key_Event_t event) {

    switch (event) {
        case KEY_EVENT_LEFT:
            // 向左翻页（循环）
            ui_data.auto_switch = 0; // 关闭自动切换
            if (ui_data.current_page == 0) {
                ui_data.current_page = UI_PAGE_MAX - 1; // 从右边接上
            } else {
                ui_data.current_page--;
            }
            ui_data.page_switch_time = HAL_GetTick(); // 重置切换时间
            break;

        case KEY_EVENT_RIGHT:
            // 向右翻页（循环）
            ui_data.auto_switch = 0; // 关闭自动切换
            ui_data.current_page = (ui_data.current_page + 1) % UI_PAGE_MAX;
            ui_data.page_switch_time = HAL_GetTick(); // 重置切换时间
            break;

        case KEY_EVENT_AUTO:
            // 切换自动翻页模式
            ui_data.auto_switch = !ui_data.auto_switch;
            ui_data.page_switch_time = HAL_GetTick(); // 重置切换时间
            break;

        case KEY_EVENT_MOTOR_TOGGLE:
            // 电机开关切换
            extern Motor_Control_t motor_ctrl;
            motor_ctrl.enable = !motor_ctrl.enable;
            motor_enable(motor_ctrl.enable);
            break;

        case KEY_EVENT_CALIBRATE:
            // 机械中值校准
            motor_calibrate_mechanical_zero();
            break;

        default:
            break;
    }
}

/**
 * @brief 按键扫描定时器回调函数 - 覆盖FreeRTOS中的弱函数
 * 在中断上下文中执行，20ms周期调用
 */
void KeyScanTimerCallback(void *argument)
{
    uint32_t current_time = HAL_GetTick();
    const uint32_t debounce_time = 50;   // 50ms消抖时间
    const uint32_t long_press_time = 3000; // 3秒长按时间

    // 扫描KEY1 (PE2) - 向左翻页/长按校准
    uint8_t key1_current = !HAL_GPIO_ReadPin(KEY1_GPIO_Port, KEY1_Pin); // 按下为低电平，取反
    if (key1_current && !key_state.key1_pressed) {
        // 按键按下
        key_state.key1_pressed = 1;
        key_state.key1_press_time = current_time;
        key_state.key1_long_handled = 0; // 重置长按处理标志
    } else if (key1_current && key_state.key1_pressed) {
        // 按键持续按下，检查长按
        if (!key_state.key1_long_handled &&
            (current_time - key_state.key1_press_time > long_press_time)) {
            uint32_t event = KEY_EVENT_CALIBRATE;
            osMessageQueuePut(KeyEventQueueHandle, &event, 0, 0);
            key_state.key1_long_handled = 1; // 标记已处理长按
        }
    } else if (!key1_current && key_state.key1_pressed) {
        // 按键释放
        if (!key_state.key1_long_handled &&
            (current_time - key_state.key1_press_time > debounce_time)) {
            uint32_t event = KEY_EVENT_LEFT;
            osMessageQueuePut(KeyEventQueueHandle, &event, 0, 0); // 发送到队列
        }
        key_state.key1_pressed = 0;
        key_state.key1_long_handled = 0;
    }

    // 扫描KEY2 (PE3) - 向右翻页
    uint8_t key2_current = !HAL_GPIO_ReadPin(KEY2_GPIO_Port, KEY2_Pin);
    if (key2_current && !key_state.key2_pressed) {
        key_state.key2_pressed = 1;
        key_state.key2_press_time = current_time;
    } else if (!key2_current && key_state.key2_pressed) {
        if (current_time - key_state.key2_press_time > debounce_time) {
            uint32_t event = KEY_EVENT_RIGHT;
            osMessageQueuePut(KeyEventQueueHandle, &event, 0, 0); // 发送到队列
        }
        key_state.key2_pressed = 0;
    }

    // 扫描KEY3 (PE4) - 自动翻页切换/长按电机开关
    uint8_t key3_current = !HAL_GPIO_ReadPin(KEY3_GPIO_Port, KEY3_Pin);
    if (key3_current && !key_state.key3_pressed) {
        key_state.key3_pressed = 1;
        key_state.key3_press_time = current_time;
        key_state.key3_long_handled = 0; // 重置长按处理标志
    } else if (key3_current && key_state.key3_pressed) {
        // 按键持续按下，检查长按
        if (!key_state.key3_long_handled &&
            (current_time - key_state.key3_press_time > long_press_time)) {
            uint32_t event = KEY_EVENT_MOTOR_TOGGLE;
            osMessageQueuePut(KeyEventQueueHandle, &event, 0, 0);
            key_state.key3_long_handled = 1; // 标记已处理长按
        }
    } else if (!key3_current && key_state.key3_pressed) {
        if (!key_state.key3_long_handled &&
            (current_time - key_state.key3_press_time > debounce_time)) {
            uint32_t event = KEY_EVENT_AUTO;
            osMessageQueuePut(KeyEventQueueHandle, &event, 0, 0); // 发送到队列
        }
        key_state.key3_pressed = 0;
        key_state.key3_long_handled = 0;
    }
}

/**
 * @brief 按键任务主函数 - 从队列接收按键事件并处理
 */
void StartKeyTask(void *argument)
{
    // 初始化按键
    key_init();

    uint32_t key_event;

    for(;;)
    {
        // 从队列接收按键事件，阻塞等待
        if (osMessageQueueGet(KeyEventQueueHandle, &key_event, 0, osWaitForever) == osOK) {
            // 处理按键事件
            key_process_event((Key_Event_t)key_event);
        }
    }
}
