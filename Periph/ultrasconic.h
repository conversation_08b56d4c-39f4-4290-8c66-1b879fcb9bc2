#ifndef ULTRASCONIC_H
#define ULTRASCONIC_H

#include "sys.h"

// 超声波传感器参数定义
#define ULTRASONIC_TIMEOUT          30000   // 超时时间(μs) - 对应最大测距5m
#define ULTRASONIC_SOUND_SPEED      340.0f  // 声速(m/s)
#define ULTRASONIC_MAX_DISTANCE     400.0f  // 最大测距(cm)
#define ULTRASONIC_MIN_DISTANCE     2.0f    // 最小测距(cm)

// 避障和跟随参数
#define OBSTACLE_THRESHOLD          20.0f   // 避障阈值(cm)
#define FOLLOW_TARGET_DISTANCE      30.0f   // 跟随目标距离(cm)
#define FOLLOW_TOLERANCE            5.0f    // 跟随距离容差(cm)
#define TURN_SPEED                  200     // 避障转向速度

// WiFi模式指令定义
#define WIFI_CMD_AVOID_ON           '5'     // 开启避障模式
#define WIFI_CMD_FOLLOW_ON          '6'     // 关闭避障，开启跟随
#define WIFI_CMD_ALL_OFF            '7'     // 关闭所有功能

// 超声波工作模式
typedef enum {
    ULTRASONIC_MODE_OFF = 0,        // 关闭
    ULTRASONIC_MODE_AVOID,          // 避障模式
    ULTRASONIC_MODE_FOLLOW          // 跟随模式
} Ultrasonic_Mode_t;

// 超声波数据结构
typedef struct {
    float distance;                 // 当前测距值(cm)
    float distance_buffer[5];       // 距离滤波缓冲区
    uint8_t buffer_index;           // 缓冲区索引
    uint32_t last_measure_time;     // 上次测量时间
    Ultrasonic_Mode_t mode;         // 当前工作模式
    uint8_t measurement_valid;      // 测量数据有效标志
} Ultrasonic_Data_t;

// 函数声明
void ultrasonic_init(void);                     // 超声波初始化
float ultrasonic_measure(void);                 // 超声波测距
void ultrasonic_trigger(void);                  // 发送触发脉冲
float ultrasonic_calculate_distance(uint32_t time_us); // 计算距离
void ultrasonic_process_mode_command(uint8_t cmd);     // 处理模式指令
void ultrasonic_obstacle_avoidance(float distance);   // 避障逻辑
void ultrasonic_target_following(float distance);     // 跟随逻辑
float ultrasonic_get_distance(void) ;             // 获取距离
Ultrasonic_Mode_t ultrasonic_get_mode(void);          // 获取当前模式

// 全局变量声明
extern osMessageQueueId_t UltrasonicDataQueueHandle;

#endif //ULTRASCONIC_H
