#include "ui.h"
#include <stdlib.h> // 包含abs函数

// 原有简单UI代码 - 已注释，升级为多页面显示
/*
#include "encoder.h"
#include "oled.h"
#include "mpu_data.h"

void StartOledTask(void *argument)
{
    for(;;)
    {
        if (osSemaphoreAcquire(DisplayMutexHandle, 10) == osOK) {
            OLED_Clear();
            OLED_ShowStr(0,0,"tick:",16);
            OLED_ShowStr(0,1,"count:",6);
            OLED_ShowNum(40,0,osKernelGetTickCount(),8,16);
            OLED_ShowNum(48,1,count,8,16);
            osSemaphoreRelease(DisplayMutexHandle);
        }
        osDelay(50);
    }
}
*/

// 全局变量定义
UI_Data_t ui_data = {0};

// 共享数据缓存，避免队列冲突
static MPU6050_Data_t cached_mpu_data = {0};
static float cached_ultrasonic_distance = 0.0f;
static uint32_t last_mpu_update = 0;
static uint32_t last_ultrasonic_update = 0;

/**
 * @brief UI系统初始化
 */
void ui_init(void) {
    ui_data.error_code = 0;
    ui_data.current_page = UI_PAGE_SYSTEM;
    ui_data.page_switch_time = HAL_GetTick();
    ui_data.auto_switch = 1; // 默认开启自动切换
}

/**
 * @brief 检查系统错误码
 * 检查各个模块的运行状态，返回第一个发现的错误码
 */
void ui_check_error_codes(void) {
    // 先清除错误码，然后检查当前状态
    ui_data.error_code = 0;

    // 检查MPU6050状态
    if (g_mpu_state.g_mpu_state < 0) {
        ui_data.error_code = MPU_ERROR;
        return;
    }

    // 暂时简化WiFi错误检查，后续可以通过队列状态等方式判断
    // 如果WiFi队列长时间无数据，可能表示WiFi连接异常
    uint32_t queue_count = osMessageQueueGetCount(WiFiCmdQueueHandle);
    static uint32_t last_check_time = 0;
    static uint32_t last_queue_count = 0;

    uint32_t current_time = HAL_GetTick();
    if (current_time - last_check_time > 10000) { // 10秒检查一次
        if (queue_count == last_queue_count) {
            // 队列长时间无变化，可能WiFi有问题，但不一定是错误
            // 这里暂时不设置错误，避免误报
        }
        last_check_time = current_time;
        last_queue_count = queue_count;
    }

    // 注意：错误码已在函数开始时清除，只有检测到实际错误才会设置
}

/**
 * @brief 获取当前错误码
 */
int16_t ui_get_error_code(void) {
    return ui_data.error_code;
}

void ui_display_mode(void) {
    // 显示屏幕滚动模式
    if (ui_data.auto_switch)
        OLED_ShowStr(80, 1, "(Auto)", 8);
    else
        OLED_ShowStr(80, 1, "      ", 8);
}
/**
 * @brief 显示系统状态页面 - 修复布局，128×64只能显示4行16px字体
 */
void ui_display_system_status(void) {
    // 检查错误码
    ui_check_error_codes();

    if (ui_data.error_code != 0) {
        // 有错误时显示错误信息 - 使用8px字体显示更多信息
        OLED_ShowStr(0, 0, "ERROR:", 8);
        OLED_ShowNum(42, 0, abs(ui_data.error_code), 3, 8);

        // 显示错误类型
        switch (ui_data.error_code) {
            case ESP_ERROR:
                OLED_ShowStr(0, 1, "WiFi Failed", 8);
                break;
            case MPU_ERROR:
                OLED_ShowStr(0, 1, "MPU Error", 8);
                break;
            case PID_ERROR:
                OLED_ShowStr(0, 1, "PID Error", 8);
                break;
            default:
                OLED_ShowStr(0, 1, "Unknown", 8);
                break;
        }
        OLED_ShowStr(0, 2, "Check System", 8);
        OLED_ShowStr(0, 3, "Press KEY3", 8);
    } else {
        // 无错误时显示正常状态 - 4行16px布局
        OLED_ShowStr(0, 0, "System OK", 16);        // 行0

        // 显示运行时间
        OLED_ShowStr(0, 2, "Time:", 16);            // 行1
        OLED_ShowNum(40, 2, HAL_GetTick() / 1000, 5, 16);
        OLED_ShowStr(80, 2, "s", 16);

        // 显示当前模式
        OLED_ShowStr(0, 4, "Mode:", 16);            // 行2
        Ultrasonic_Mode_t mode = ultrasonic_get_mode();
        switch (mode) {
            case ULTRASONIC_MODE_AVOID:
                OLED_ShowStr(48, 4, "Avoid", 16);
                break;
            case ULTRASONIC_MODE_FOLLOW:
                OLED_ShowStr(48, 4, "Follow", 16);
                break;
            default:
                OLED_ShowStr(48, 4, "Manual", 16);
                break;
        }

        // WiFi状态
        OLED_ShowStr(0, 6, "WiFi:", 16);            // 行3
        if (is_wifi_ready()) {
            OLED_ShowStr(48, 6, "Ready", 16);
        } else {
            OLED_ShowStr(48, 6, "Init..", 16);
        }
    }
}

/**
 * @brief 显示传感器数据页面 - 使用缓存数据，避免队列冲突
 */
void ui_display_sensor_data(void) {
    // 检查缓存数据是否有效（5秒内更新过）
    uint32_t current_time = HAL_GetTick();
    if (current_time - last_mpu_update < 5000) {
        // 显示陀螺仪数据 - 4行16px布局
        OLED_ShowStr(0, 0, "MPU6050", 16);          // 行0

        OLED_ShowStr(0, 2, "P:", 16);               // 行1 - Pitch
        OLED_ShowFloatFixed(24, 2, cached_mpu_data.pitch, 3, 16, 8);

        OLED_ShowStr(0, 4, "R:", 16);               // 行2 - Roll
        OLED_ShowFloatFixed(24, 4, cached_mpu_data.roll, 3, 16, 8);

        OLED_ShowStr(0, 6, "Y:", 16);               // 行3 - Yaw
        OLED_ShowFloatFixed(24, 6, cached_mpu_data.yaw, 3, 16, 8);
    } else {
        // 无数据时显示提示
        OLED_ShowStr(0, 0, "MPU6050", 16);          // 行0
        OLED_ShowStr(0, 2, "No Data", 16);          // 行1
        OLED_ShowStr(0, 4, "Check I2C", 16);        // 行2
        OLED_ShowStr(0, 6, "Connection", 16);       // 行3
    }
}
/**
 * @brief 显示电机状态页面 - 4行16px布局
 */
void ui_display_motor_status(void) {
    extern Motor_Control_t motor_ctrl;

    // 显示电机速度 - 4行16px布局
    OLED_ShowStr(0, 0, "Motor", 16);                // 行0

    OLED_ShowStr(0, 2, "L:", 16);                   // 行1 - 左电机
    OLED_ShowNum(24, 2, Encoder_GetSpeed(1), 4, 16);
    OLED_ShowStr(72, 2, "R:", 16);
    OLED_ShowNum(96, 2, Encoder_GetSpeed(2), 4, 16);

    // 显示使能状态和机械中值
    OLED_ShowStr(0, 4, "EN:", 16);                  // 行2 - 使能状态
    if (motor_ctrl.enable) {
        OLED_ShowStr(32, 4, "ON", 16);
    } else {
        OLED_ShowStr(32, 4, "OFF", 16);
    }
    OLED_ShowStr(64, 4, "Z:", 16);                  // 机械中值
    OLED_ShowFloatFixed(80, 4, motor_get_mechanical_zero(), 1, 16, 4);

    // 显示编码器计数（简化显示）
    OLED_ShowStr(0, 6, "CNT:", 16);                 // 行3 - 计数
    OLED_ShowNum(40, 6, Encoder_GetCount(1), 4, 16);
    OLED_ShowNum(88, 6, Encoder_GetCount(2), 4, 16);
}

/**
 * @brief 显示超声波数据页面 - 使用缓存数据，4行16px布局
 */
void ui_display_ultrasonic_data(void) {
    // 检查缓存数据是否有效（3秒内更新过）
    uint32_t current_time = HAL_GetTick();
    if (current_time - last_ultrasonic_update < 3000 && cached_ultrasonic_distance > 0) {
        // 显示距离 - 4行16px布局
        OLED_ShowStr(0, 0, "Distance", 16);         // 行0

        OLED_ShowFloatFixed(0, 2, cached_ultrasonic_distance, 1, 16, 6); // 行1
        OLED_ShowStr(64, 2, "cm", 16);

        // 显示状态指示
        if (cached_ultrasonic_distance < OBSTACLE_THRESHOLD) {
            OLED_ShowStr(0, 4, "OBSTACLE", 16);     // 行2
        } else if (cached_ultrasonic_distance > ULTRASONIC_MAX_DISTANCE * 0.8f) {
            OLED_ShowStr(0, 4, "FAR", 16);
        } else {
            OLED_ShowStr(0, 4, "NORMAL", 16);
        }
    } else {
        // 无数据时显示提示
        OLED_ShowStr(0, 0, "Distance", 16);         // 行0
        OLED_ShowStr(0, 2, "No Data", 16);          // 行1
        OLED_ShowStr(0, 4, "Measuring", 16);        // 行2
    }

    // 显示当前模式
    Ultrasonic_Mode_t mode = ultrasonic_get_mode();
    switch (mode) {                                 // 行3
        case ULTRASONIC_MODE_AVOID:
            OLED_ShowStr(0, 6, "Mode:Avoid", 16);
            break;
        case ULTRASONIC_MODE_FOLLOW:
            OLED_ShowStr(0, 6, "Mode:Follow", 16);
            break;
        default:
            OLED_ShowStr(0, 6, "Mode:Off", 16);
            break;
    }
}

/**
 * @brief 页面切换逻辑
 */
void ui_switch_page(void) {
    uint32_t current_time = HAL_GetTick();
    const uint32_t page_duration = 5000; // 每页显示5秒（按要求修改）

    if (ui_data.auto_switch && (current_time - ui_data.page_switch_time > page_duration)) {
        // 如果有错误，停留在系统状态页面
        if (ui_data.error_code != 0) {
            ui_data.current_page = UI_PAGE_SYSTEM;
        } else {
            // 正常情况下循环切换页面
            ui_data.current_page = (ui_data.current_page + 1) % UI_PAGE_MAX;
        }
        ui_data.page_switch_time = current_time;
    }
}
/**
 * @brief 数据更新函数 - 在UI任务中更新缓存数据
 */
void ui_update_cached_data(void) {

    // 尝试更新MPU数据缓存
    cached_mpu_data = get_mpu_data();
    last_mpu_update = HAL_GetTick();

    // 尝试更新超声波数据缓存
    cached_ultrasonic_distance = ultrasonic_get_distance();
    last_ultrasonic_update = HAL_GetTick();
}

/**
 * @brief 优化后的OLED显示任务 - 解决闪烁和队列冲突问题
 */
void StartOledTask(void *argument) {
    // 初始化UI系统
    ui_init();

    uint8_t last_page = 255; // 记录上次页面，避免不必要的清屏

    for(;;) {
        // 更新缓存数据
        ui_update_cached_data();

        // 获取显示互斥锁
        if (osSemaphoreAcquire(DisplayMutexHandle, 10) == osOK) {

            // 页面切换逻辑
            ui_switch_page();

            // 显示当前模式
            ui_display_mode();

            // 只有页面切换时才清屏，减少闪烁
            if (ui_data.current_page != last_page) {
                OLED_Clear();
                last_page = ui_data.current_page;
            }

            // 根据当前页面显示相应内容
            switch (ui_data.current_page) {
                case UI_PAGE_SYSTEM:
                    ui_display_system_status();
                    break;

                case UI_PAGE_SENSOR:
                    ui_display_sensor_data();
                    break;

                case UI_PAGE_MOTOR:
                    ui_display_motor_status();
                    break;

                case UI_PAGE_ULTRASONIC:
                    ui_display_ultrasonic_data();
                    break;

                default:
                    ui_data.current_page = UI_PAGE_SYSTEM;
                    ui_display_system_status();
                    break;
            }

            // 释放显示互斥锁
            osSemaphoreRelease(DisplayMutexHandle);
        }

        // 任务延时 - 500ms刷新间隔，减少系统负载
        osDelay(100);
    }
}

/*
 * UI系统运行逻辑详解：
 *
 * 1. 多页面显示系统：
 *    - 页面0：系统状态 - 错误码检查、运行时间、当前模式
 *    - 页面1：传感器数据 - MPU6050陀螺仪数据(pitch/roll/yaw)
 *    - 页面2：电机状态 - 左右电机速度、计数、使能状态、WiFi指令
 *    - 页面3：超声波数据 - 距离测量、状态指示、工作模式
 *
 * 2. 智能页面切换：
 *    - 自动模式：每页显示3秒后自动切换
 *    - 错误优先：有错误时停留在系统状态页面
 *    - 页面指示器：底部显示当前页面和总页面数
 *
 * 3. 错误处理机制：
 *    - 实时检查MPU6050、WiFi等模块状态
 *    - 错误码优先显示，便于快速诊断问题
 *    - 错误类型分类显示，提供具体故障信息
 *
 * 4. 数据获取策略：
 *    - 非阻塞队列读取，避免任务阻塞
 *    - 数据无效时显示友好提示信息
 *    - 实时状态更新，反映系统当前状态
 *
 * 5. 显示优化：
 *    - 利用128×64分辨率，显示更多信息
 *    - 合理的字体大小搭配(16和8像素)
 *    - 清晰的布局设计，信息层次分明
 *    - 200ms刷新间隔，平衡流畅度和性能
 */