#ifndef UI_H
#define UI_H

#include "cmsis_os.h"
#include "sys.h"
#include "mpu_data.h"
#include "motor_driver.h"
#include "ultrasconic.h"

// UI显示页面定义
typedef enum {
    UI_PAGE_SYSTEM = 0,     // 系统状态页面
    UI_PAGE_SENSOR,         // 传感器数据页面
    UI_PAGE_MOTOR,          // 电机状态页面
    UI_PAGE_ULTRASONIC,     // 超声波数据页面
    UI_PAGE_MAX             // 页面总数
} UI_Page_t;

// UI显示数据结构
typedef struct {
    int16_t error_code;         // 系统错误码
    uint8_t current_page;       // 当前显示页面
    uint32_t page_switch_time;  // 页面切换时间
    uint8_t auto_switch;        // 自动切换标志
} UI_Data_t;

// 函数声明
void ui_init(void);                                    // UI初始化
void ui_display_system_status(void);                   // 显示系统状态
void ui_display_sensor_data(void);                     // 显示传感器数据
void ui_display_motor_status(void);                    // 显示电机状态
void ui_display_ultrasonic_data(void);                 // 显示超声波数据
void ui_check_error_codes(void);                       // 检查错误码
void ui_switch_page(void);                             // 页面切换
int16_t ui_get_error_code(void);                       // 获取错误码

// 全局变量声明
extern osSemaphoreId_t DisplayMutexHandle;
extern UI_Data_t ui_data;

#endif
